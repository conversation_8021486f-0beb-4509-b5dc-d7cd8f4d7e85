"""
Modulo per la gestione delle notifiche (email e SMS) per le comande.
Invia automaticamente il codice comanda ai responsabili.
"""

import logging
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, Dict, Any
from datetime import datetime
import os

# Configurazione email (da variabili d'ambiente o configurazione)
SMTP_SERVER = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
SMTP_USERNAME = os.getenv('SMTP_USERNAME', '')
SMTP_PASSWORD = os.getenv('SMTP_PASSWORD', '')
EMAIL_FROM = os.getenv('EMAIL_FROM', SMTP_USERNAME)

def crea_template_email_comanda(codice_comanda: str, tipo_comanda: str, 
                               responsabile_nome: str, descrizione: str = "",
                               cantiere_nome: str = "") -> Dict[str, str]:
    """
    Crea il template email per la notifica di una nuova comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        tipo_comanda: Tipo di comanda (POSA, COLLEGAMENTO, etc.)
        responsabile_nome: Nome del responsabile
        descrizione: Descrizione della comanda
        cantiere_nome: Nome del cantiere
        
    Returns:
        Dict con subject e body dell'email
    """
    
    # Mappa dei tipi comanda per descrizioni più user-friendly
    tipo_descrizioni = {
        'POSA': 'Posa Cavi',
        'COLLEGAMENTO_PARTENZA': 'Collegamento Lato Partenza',
        'COLLEGAMENTO_ARRIVO': 'Collegamento Lato Arrivo',
        'CERTIFICAZIONE': 'Certificazione Cavi'
    }
    
    tipo_friendly = tipo_descrizioni.get(tipo_comanda, tipo_comanda)
    
    subject = f"Nuova Comanda {codice_comanda} - {tipo_friendly}"
    
    body = f"""
Gentile {responsabile_nome},

Le è stata assegnata una nuova comanda di lavoro:

🔧 DETTAGLI COMANDA:
• Codice Comanda: {codice_comanda}
• Tipo Lavoro: {tipo_friendly}
• Cantiere: {cantiere_nome}
• Descrizione: {descrizione}

📱 ISTRUZIONI:
1. Utilizzare il codice comanda {codice_comanda} nell'app mobile
2. Il codice è necessario per accedere ai dettagli del lavoro
3. Aggiornare lo stato dei lavori tramite l'app

⚠️ IMPORTANTE:
Conservare questo codice per accedere alla comanda dall'app mobile.

---
Sistema CMS - Gestione Cantieri
Data invio: {datetime.now().strftime('%d/%m/%Y %H:%M')}
"""
    
    return {
        'subject': subject,
        'body': body
    }

def invia_email(destinatario: str, subject: str, body: str) -> bool:
    """
    Invia un'email utilizzando SMTP.
    
    Args:
        destinatario: Indirizzo email del destinatario
        subject: Oggetto dell'email
        body: Corpo dell'email
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    if not SMTP_USERNAME or not SMTP_PASSWORD:
        logging.warning("⚠️ Configurazione SMTP non completa, email non inviata")
        return False
        
    try:
        # Crea il messaggio
        message = MIMEMultipart()
        message["From"] = EMAIL_FROM
        message["To"] = destinatario
        message["Subject"] = subject
        
        # Aggiungi il corpo del messaggio
        message.attach(MIMEText(body, "plain", "utf-8"))
        
        # Crea connessione SMTP sicura
        context = ssl.create_default_context()
        
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls(context=context)
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            
            # Invia l'email
            text = message.as_string()
            server.sendmail(EMAIL_FROM, destinatario, text)
            
        logging.info(f"✅ Email inviata con successo a {destinatario}")
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore nell'invio email a {destinatario}: {str(e)}")
        return False

def invia_notifica_comanda(codice_comanda: str, tipo_comanda: str, 
                          responsabile_nome: str, responsabile_email: Optional[str] = None,
                          responsabile_telefono: Optional[str] = None,
                          descrizione: str = "", cantiere_nome: str = "") -> Dict[str, bool]:
    """
    Invia notifica di una nuova comanda al responsabile.
    Supporta email e SMS (SMS da implementare).
    
    Args:
        codice_comanda: Codice univoco della comanda
        tipo_comanda: Tipo di comanda
        responsabile_nome: Nome del responsabile
        responsabile_email: Email del responsabile (opzionale)
        responsabile_telefono: Telefono del responsabile (opzionale)
        descrizione: Descrizione della comanda
        cantiere_nome: Nome del cantiere
        
    Returns:
        Dict con risultati invio: {'email': bool, 'sms': bool}
    """
    
    risultati = {'email': False, 'sms': False}
    
    # Invia email se disponibile
    if responsabile_email:
        template = crea_template_email_comanda(
            codice_comanda, tipo_comanda, responsabile_nome, 
            descrizione, cantiere_nome
        )
        
        risultati['email'] = invia_email(
            responsabile_email, 
            template['subject'], 
            template['body']
        )
    
    # Invia SMS se disponibile (da implementare in Step 4)
    if responsabile_telefono:
        logging.info(f"📱 SMS per {responsabile_telefono} - da implementare in Step 4")
        # risultati['sms'] = invia_sms(responsabile_telefono, messaggio_sms)
    
    return risultati

def test_configurazione_email() -> bool:
    """
    Testa la configurazione email inviando un messaggio di test.
    
    Returns:
        True se la configurazione è corretta, False altrimenti
    """
    
    if not SMTP_USERNAME or not SMTP_PASSWORD:
        logging.error("❌ Configurazione SMTP mancante")
        return False
        
    try:
        # Test connessione SMTP
        context = ssl.create_default_context()
        
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls(context=context)
            server.login(SMTP_USERNAME, SMTP_PASSWORD)
            
        logging.info("✅ Configurazione email corretta")
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore configurazione email: {str(e)}")
        return False

def invia_email_test(destinatario: str) -> bool:
    """
    Invia un'email di test per verificare il funzionamento.
    
    Args:
        destinatario: Indirizzo email di test
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    subject = "Test Sistema Notifiche CMS"
    body = f"""
Questo è un messaggio di test dal sistema CMS.

Se ricevi questo messaggio, la configurazione email è corretta.

Data test: {datetime.now().strftime('%d/%m/%Y %H:%M')}

---
Sistema CMS - Gestione Cantieri
"""
    
    return invia_email(destinatario, subject, body)

# Funzioni di utilità per logging delle notifiche
def log_notifica_inviata(codice_comanda: str, destinatario: str, tipo: str, successo: bool):
    """
    Registra nel log l'invio di una notifica.
    
    Args:
        codice_comanda: Codice della comanda
        destinatario: Email o telefono del destinatario
        tipo: 'EMAIL' o 'SMS'
        successo: True se l'invio è riuscito
    """
    
    status = "✅ INVIATA" if successo else "❌ FALLITA"
    logging.info(f"📧 Notifica {tipo} per comanda {codice_comanda} a {destinatario}: {status}")

def ottieni_configurazione_notifiche() -> Dict[str, Any]:
    """
    Restituisce la configurazione attuale delle notifiche.
    
    Returns:
        Dict con la configurazione
    """
    
    return {
        'smtp_server': SMTP_SERVER,
        'smtp_port': SMTP_PORT,
        'smtp_username': SMTP_USERNAME,
        'email_from': EMAIL_FROM,
        'smtp_configurato': bool(SMTP_USERNAME and SMTP_PASSWORD)
    }
