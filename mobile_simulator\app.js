// Configurazione API
const API_BASE_URL = 'http://localhost:8002/api';

// Stato dell'applicazione
let currentUser = null;
let sessionToken = null;
let currentComanda = null;

// Utility functions
function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

function showAlert(message, type = 'error') {
    const alertDiv = document.getElementById('login-alert');
    alertDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        alertDiv.innerHTML = '';
    }, 5000);
}

function showLoading() {
    showScreen('loading-screen');
}

function formatDate(dateString) {
    if (!dateString) return 'Non specificata';
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT');
}

function getStatusClass(status) {
    const statusMap = {
        'CREATA': 'status-creata',
        'ASSEGNATA': 'status-assegnata',
        'COMPLETATA': 'status-completata'
    };
    return statusMap[status] || 'status-creata';
}

function getTipoComandaFriendly(tipo) {
    const tipoMap = {
        'POSA': 'Posa Cavi',
        'COLLEGAMENTO_PARTENZA': 'Collegamento Partenza',
        'COLLEGAMENTO_ARRIVO': 'Collegamento Arrivo',
        'CERTIFICAZIONE': 'Certificazione'
    };
    return tipoMap[tipo] || tipo;
}

// API calls
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || `HTTP error! status: ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Login functionality
document.getElementById('login-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const codiceComanda = document.getElementById('codice-comanda').value.trim();
    const contatto = document.getElementById('contatto').value.trim();
    
    if (!codiceComanda || !contatto) {
        showAlert('Inserisci tutti i campi richiesti');
        return;
    }
    
    showLoading();
    
    try {
        const loginData = await apiCall('/mobile/login', {
            method: 'POST',
            body: JSON.stringify({
                codice_comanda: codiceComanda,
                contatto: contatto
            })
        });
        
        if (loginData.success) {
            currentUser = {
                nome: loginData.responsabile_nome,
                id: loginData.responsabile_id
            };
            sessionToken = loginData.session_token;
            
            // Carica i dettagli della comanda
            await loadComandaDetails(codiceComanda);
            
            showAlert('Login effettuato con successo!', 'success');
            setTimeout(() => {
                showScreen('comanda-screen');
            }, 1000);
        } else {
            showAlert(loginData.message || 'Errore nel login');
            showScreen('login-screen');
        }
    } catch (error) {
        showAlert(`Errore nel login: ${error.message}`);
        showScreen('login-screen');
    }
});

// Load comanda details
async function loadComandaDetails(codiceComanda) {
    try {
        const comanda = await apiCall(`/mobile/comanda/${codiceComanda}?session_token=${sessionToken}`);
        currentComanda = comanda;
        
        const detailsHtml = `
            <div class="comanda-card">
                <div class="comanda-header">
                    <div class="comanda-code">${comanda.codice_comanda}</div>
                    <div class="comanda-status ${getStatusClass(comanda.stato)}">${comanda.stato}</div>
                </div>
                
                <div style="margin-bottom: 12px;">
                    <strong>${getTipoComandaFriendly(comanda.tipo_comanda)}</strong>
                </div>
                
                <div style="margin-bottom: 12px; color: #666; font-size: 14px;">
                    ${comanda.descrizione}
                </div>
                
                <div style="margin-bottom: 12px; font-size: 14px;">
                    <strong>Cantiere:</strong> ${comanda.cantiere_nome}<br>
                    <strong>Responsabile:</strong> ${comanda.responsabile}<br>
                    <strong>Data Creazione:</strong> ${formatDate(comanda.data_creazione)}<br>
                    <strong>Scadenza:</strong> ${formatDate(comanda.data_scadenza)}
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${comanda.numero_cavi}</div>
                        <div class="stat-label">Cavi Totali</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${comanda.cavi_completati}</div>
                        <div class="stat-label">Completati</div>
                    </div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${comanda.progresso_percentuale}%"></div>
                </div>
                <div style="text-align: center; font-size: 14px; color: #666; margin-top: 8px;">
                    Progresso: ${comanda.progresso_percentuale}%
                </div>
            </div>
        `;
        
        document.getElementById('comanda-details').innerHTML = detailsHtml;
    } catch (error) {
        showAlert(`Errore nel caricamento dettagli: ${error.message}`);
    }
}

// Show cavi
async function showCavi() {
    if (!currentComanda) return;
    
    showLoading();
    
    try {
        const cavi = await apiCall(`/mobile/comanda/${currentComanda.codice_comanda}/cavi?session_token=${sessionToken}`);
        
        let caviHtml = '';
        
        if (cavi.length === 0) {
            caviHtml = '<div style="text-align: center; color: #666; padding: 40px;">Nessun cavo assegnato a questa comanda</div>';
        } else {
            cavi.forEach(cavo => {
                const statusClass = cavo.stato_installazione === 'Installato' ? 'status-installato' : 'status-non-installato';
                
                caviHtml += `
                    <div class="cavo-item">
                        <div class="cavo-header">
                            <div class="cavo-id">${cavo.id_cavo}</div>
                            <div class="cavo-status ${statusClass}">${cavo.stato_installazione}</div>
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            <strong>Tipo:</strong> ${cavo.tipologia}<br>
                            <strong>Formazione:</strong> ${cavo.formazione}<br>
                            <strong>Metri Teorici:</strong> ${cavo.metratura_teorica}m<br>
                            ${cavo.metratura_reale ? `<strong>Metri Reali:</strong> ${cavo.metratura_reale}m<br>` : ''}
                            <strong>Collegamenti:</strong> ${getCollegamentiText(cavo.collegamenti)}<br>
                            <strong>Certificato:</strong> ${cavo.certificato ? 'Sì' : 'No'}
                        </div>
                    </div>
                `;
            });
        }
        
        document.getElementById('cavi-list').innerHTML = caviHtml;
        showScreen('cavi-screen');
    } catch (error) {
        showAlert(`Errore nel caricamento cavi: ${error.message}`);
        showScreen('comanda-screen');
    }
}

function getCollegamentiText(collegamenti) {
    switch (collegamenti) {
        case 0: return 'Nessuno';
        case 1: return 'Partenza';
        case 2: return 'Arrivo';
        case 3: return 'Entrambi';
        default: return 'Sconosciuto';
    }
}

// Show comande
async function showComande() {
    if (!currentUser) return;
    
    showLoading();
    
    try {
        const comande = await apiCall(`/mobile/responsabile/${currentUser.id}/comande?session_token=${sessionToken}`);
        
        let comandeHtml = '';
        
        if (comande.length === 0) {
            comandeHtml = '<div style="text-align: center; color: #666; padding: 40px;">Nessuna comanda assegnata</div>';
        } else {
            comande.forEach(comanda => {
                comandeHtml += `
                    <div class="comanda-card" onclick="selectComanda('${comanda.codice_comanda}')" style="cursor: pointer;">
                        <div class="comanda-header">
                            <div class="comanda-code">${comanda.codice_comanda}</div>
                            <div class="comanda-status ${getStatusClass(comanda.stato)}">${comanda.stato}</div>
                        </div>
                        
                        <div style="margin-bottom: 8px;">
                            <strong>${getTipoComandaFriendly(comanda.tipo_comanda)}</strong>
                        </div>
                        
                        <div style="margin-bottom: 8px; color: #666; font-size: 14px;">
                            ${comanda.descrizione}
                        </div>
                        
                        <div style="margin-bottom: 8px; font-size: 14px;">
                            <strong>Cantiere:</strong> ${comanda.cantiere_nome}<br>
                            <strong>Data:</strong> ${formatDate(comanda.data_creazione)}
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${comanda.progresso_percentuale}%"></div>
                        </div>
                        <div style="text-align: center; font-size: 12px; color: #666; margin-top: 4px;">
                            ${comanda.progresso_percentuale}%
                        </div>
                    </div>
                `;
            });
        }
        
        document.getElementById('comande-list').innerHTML = comandeHtml;
        showScreen('comande-screen');
    } catch (error) {
        showAlert(`Errore nel caricamento comande: ${error.message}`);
        showScreen('comanda-screen');
    }
}

// Select comanda
async function selectComanda(codiceComanda) {
    showLoading();
    await loadComandaDetails(codiceComanda);
    showScreen('comanda-screen');
}

// Navigation functions
function showComanda() {
    showScreen('comanda-screen');
}

function logout() {
    currentUser = null;
    sessionToken = null;
    currentComanda = null;
    
    // Reset form
    document.getElementById('login-form').reset();
    document.getElementById('login-alert').innerHTML = '';
    
    showScreen('login-screen');
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    console.log('CMS Mobile App Simulator inizializzato');
    
    // Test API connection
    apiCall('/mobile/ping')
        .then(response => {
            console.log('API Mobile connessa:', response);
        })
        .catch(error => {
            console.error('API Mobile non disponibile:', error);
            showAlert('⚠️ API Mobile non disponibile. Assicurati che il backend sia in esecuzione.', 'error');
        });
});
