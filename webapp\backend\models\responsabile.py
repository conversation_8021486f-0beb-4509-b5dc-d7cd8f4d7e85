from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.sql import func

from backend.database import Base

class Responsabile(Base):
    """
    Modello SQLAlchemy per la tabella responsabili.
    Gestisce i responsabili delle comande con dati di contatto.
    """
    __tablename__ = "responsabili"

    id_responsabile = Column(Integer, primary_key=True, index=True)
    nome_responsabile = Column(String(255), nullable=False)
    telefono = Column(String(20), nullable=True, unique=True)
    email = Column(String(255), nullable=True, unique=True)
    data_creazione = Column(DateTime, nullable=False, default=func.now())
    attivo = Column(Boolean, default=True)
