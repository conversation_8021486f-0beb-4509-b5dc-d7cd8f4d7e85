<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CMS Mobile App Simulator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .mobile-container {
            max-width: 375px;
            margin: 20px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            min-height: 600px;
        }

        .header {
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            padding: 20px;
        }

        .screen {
            display: none;
        }

        .screen.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2196f3;
        }

        .btn {
            width: 100%;
            padding: 14px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #1976d2;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #757575;
        }

        .btn-secondary:hover {
            background: #616161;
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }

        .alert-error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }

        .comanda-card {
            background: #f9f9f9;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border-left: 4px solid #2196f3;
        }

        .comanda-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .comanda-code {
            font-weight: 700;
            color: #2196f3;
            font-size: 16px;
        }

        .comanda-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-creata { background: #fff3e0; color: #f57c00; }
        .status-assegnata { background: #e3f2fd; color: #1976d2; }
        .status-completata { background: #e8f5e8; color: #2e7d32; }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 12px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #2e7d32);
            transition: width 0.3s ease;
        }

        .cavo-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .cavo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .cavo-id {
            font-weight: 600;
            color: #333;
        }

        .cavo-status {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-installato { background: #e8f5e8; color: #2e7d32; }
        .status-non-installato { background: #ffebee; color: #c62828; }

        .back-btn {
            background: none;
            border: none;
            color: #2196f3;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
            padding: 8px 0;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196f3;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px 0;
        }

        .stat-item {
            background: #f5f5f5;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2196f3;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <div class="header">
            <h1>CMS Mobile</h1>
            <p>Sistema Gestione Cantieri</p>
        </div>

        <div class="content">
            <!-- Schermata Login -->
            <div id="login-screen" class="screen active">
                <h2 style="margin-bottom: 20px; text-align: center;">Accesso Comanda</h2>
                
                <div id="login-alert"></div>
                
                <form id="login-form">
                    <div class="form-group">
                        <label for="codice-comanda">Codice Comanda</label>
                        <input type="text" id="codice-comanda" placeholder="Es: POS_1_20250609..." required>
                    </div>
                    
                    <div class="form-group">
                        <label for="contatto">Email o Telefono</label>
                        <input type="text" id="contatto" placeholder="<EMAIL> o +39 ************" required>
                    </div>
                    
                    <button type="submit" class="btn" id="login-btn">
                        Accedi
                    </button>
                </form>

                <div style="margin-top: 30px; padding: 16px; background: #f0f8ff; border-radius: 8px; font-size: 14px;">
                    <strong>💡 Come testare:</strong><br>
                    1. Crea una comanda dal sistema CMS<br>
                    2. Usa il codice comanda generato<br>
                    3. Inserisci l'email/telefono del responsabile<br>
                    4. Accedi per gestire la comanda
                </div>
            </div>

            <!-- Schermata Dettagli Comanda -->
            <div id="comanda-screen" class="screen">
                <button class="back-btn" onclick="logout()">← Logout</button>
                
                <div id="comanda-details"></div>
                
                <div style="margin-top: 20px;">
                    <button class="btn" onclick="showCavi()">Visualizza Cavi</button>
                    <button class="btn btn-secondary" onclick="showComande()" style="margin-top: 10px;">Le Mie Comande</button>
                </div>
            </div>

            <!-- Schermata Lista Cavi -->
            <div id="cavi-screen" class="screen">
                <button class="back-btn" onclick="showComanda()">← Torna alla Comanda</button>
                
                <h3 style="margin-bottom: 16px;">Cavi della Comanda</h3>
                
                <div id="cavi-list"></div>
            </div>

            <!-- Schermata Le Mie Comande -->
            <div id="comande-screen" class="screen">
                <button class="back-btn" onclick="showComanda()">← Torna alla Comanda</button>
                
                <h3 style="margin-bottom: 16px;">Le Mie Comande</h3>
                
                <div id="comande-list"></div>
            </div>

            <!-- Schermata Loading -->
            <div id="loading-screen" class="screen">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Caricamento...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
