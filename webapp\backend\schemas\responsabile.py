from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime

class ResponsabileBase(BaseModel):
    """Schema base per i responsabili."""
    nome_responsabile: str
    telefono: Optional[str] = None
    email: Optional[str] = None
    
    @validator('telefono', 'email')
    def almeno_un_contatto(cls, v, values):
        """Valida che almeno uno tra telefono ed email sia presente."""
        if not v and not values.get('telefono') and not values.get('email'):
            raise ValueError('Almeno uno tra telefono ed email deve essere specificato')
        return v

class ResponsabileCreate(ResponsabileBase):
    """Schema per la creazione di un responsabile."""
    pass

class ResponsabileUpdate(BaseModel):
    """Schema per l'aggiornamento di un responsabile."""
    nome_responsabile: Optional[str] = None
    telefono: Optional[str] = None
    email: Optional[str] = None
    attivo: Optional[bool] = None

class ResponsabileInDB(ResponsabileBase):
    """Schema per un responsabile nel database."""
    id_responsabile: int
    data_creazione: datetime
    attivo: bool

    class Config:
        orm_mode = True

class ResponsabileResponse(ResponsabileInDB):
    """Schema di risposta per un responsabile."""
    pass
